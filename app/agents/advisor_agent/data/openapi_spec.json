{"openapi": "3.0.3", "info": {"title": "Wealth Management API", "description": "API for wealth management operations including accounts, balances, positions, and activities", "version": "1.0.0"}, "servers": [], "paths": {"/api/domain/wealthdomain/getAccountsFilteredByAccountStatus": {"get": {"summary": "Get Accounts list", "description": "Get all accounts following the filters", "operationId": "getAccountsList", "parameters": [{"name": "_page", "in": "query", "required": false, "schema": {"type": "string", "default": "0"}}, {"name": "_size", "in": "query", "required": false, "schema": {"type": "string", "default": "10"}}, {"name": "_sort", "in": "query", "required": false, "schema": {"type": "string", "default": "-dailyBalances.endingMarketValue"}}, {"name": "_fields", "in": "query", "required": false, "schema": {"type": "string", "default": "registrationType.name,primaryOwner.mailingAddress.state.name,dailyBalances.endingMarketValue,dailyBalances.fedCall,primaryOwner.id,dailyBalances.endingBalance,primaryOwner.owner.legalAddress.state.id,registrationType.id,riskTolerance,accountStatus,dailyBalances.maintenanceCall,relatedAccounts.linkedAccounts.dailyBalances.id,dailyBalances.id,dailyBalances.endingMarginBalance,relatedAccounts.id,relatedAccounts.linkedAccounts.id,primaryOwner.owner.legalAddress.id,primaryOwner.mailingAddress.id,registrationType.code,primaryOwner.owner.id,dailyBalances.endingCashBalance,primaryOwner.owner.legalAddress.state.name,accountNumber,investmentObjective,id,primaryOwner.mailingAddress.state.id,repCode,dailyBalances.endingMoneyMarketBalance,name,dailyBalances.periodEndDate"}}, {"name": "excludeStatus", "in": "query", "required": false, "schema": {"type": "string", "default": "Data%20Capture,Validation%20Failed,Validation%20Successful,Forms%20Generating,Form%20Generation%20Failed,Forms%20Generated,Options%20Pre-approval%20Pending,Options%20Approved,Initiating%20E-Signature,E-Signing,E-Signing%20Cancelled,E-Sign%20Expired,E-Signing%20Failed,E-Signed,Signing,Signed,Supervisor%20Approval%20Pending,Rep%20Action%20Required,Rejected,Approved,Account%20Setup%20In%20Progress,Account%20Setup%20Failure,Account%20Opened,Margin%20Review%20Required,Margin%20Disallowed,Margin%20Review%20Completed,Forms%20Uploading,Forms%20Failed%20to%20Load,Forms%20Successfully%20Uploaded,Wet%20Sign%20Cancelled,Ready%20for%20QC%20Review,Options%20Review%20Pending"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AccountFullDetails"}}}}}}, "tags": ["Accounts"]}}}, "components": {"schemas": {"AccountDetails": {"type": "object", "properties": {"accountNumber": {"type": "string"}, "accountStatus": {"type": "string"}, "name": {"type": "string"}, "dailyBalances": {"type": "object", "properties": {"endingCashBalance": {"type": "number", "format": "currency"}, "endingMarginBalance": {"type": "number", "format": "currency"}, "endingMarketValue": {"type": "number", "format": "currency"}, "endingMoneyMarketBalance": {"type": "number", "format": "currency"}}}, "registrationType": {"type": "string"}}}, "AssetsOverTime": {"type": "object", "properties": {"endingEts": {"type": "number", "format": "currency"}, "id": {"type": "string"}, "periodEndDate": {"type": "string", "format": "date", "description": "Date in YYYY-MM-DD format"}, "periodType": {"type": "string"}}}, "PortfolioAllocation": {"type": "object", "properties": {"securityCategory": {"type": "string"}, "totalMarketValue": {"type": "number", "format": "currency"}}}, "AdvisorAccountSummary": {"type": "object", "properties": {"advisorAccountBalances": {"type": "object", "properties": {"endingAccounts": {"type": "number"}, "endingBalance": {"type": "number", "format": "currency"}, "endingCashBalance": {"type": "number", "format": "currency"}, "endingMarginBalance": {"type": "number", "format": "currency"}}}}}, "ServiceRequestByType": {"type": "object", "properties": {"srDefinitionType": {"type": "string", "description": "Type of service request definition"}, "ticketCount": {"type": "number", "description": "Number of tickets for the service request type"}}}, "AccountFullDetails": {"type": "object", "properties": {"accountNumber": {"type": "string"}, "accountStatus": {"type": "string"}, "name": {"type": "string"}, "id": {"type": "string"}, "repCode": {"type": "string"}, "investmentObjective": {"type": "string"}, "riskTolerance": {"type": "string"}, "registrationType": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}}}, "primaryOwner": {"type": "object", "properties": {"id": {"type": "string"}, "mailingAddress": {"type": "object", "properties": {"id": {"type": "string"}, "state": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}}, "owner": {"type": "object", "properties": {"id": {"type": "string"}, "legalAddress": {"type": "object", "properties": {"id": {"type": "string"}, "state": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}}}}}}, "dailyBalances": {"type": "object", "properties": {"id": {"type": "string"}, "periodEndDate": {"type": "string", "format": "date"}, "endingBalance": {"type": "number", "format": "currency"}, "endingCashBalance": {"type": "number", "format": "currency"}, "endingMarginBalance": {"type": "number", "format": "currency"}, "endingMarketValue": {"type": "number", "format": "currency"}, "endingMoneyMarketBalance": {"type": "number", "format": "currency"}, "fedCall": {"type": "number", "format": "currency"}, "maintenanceCall": {"type": "number", "format": "currency"}}}, "relatedAccounts": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "linkedAccounts": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "dailyBalances": {"type": "object", "properties": {"id": {"type": "string"}}}}}}}}}}}, "AccountBalanceDetails": {"type": "object", "properties": {"id": {"type": "string"}, "accountNumber": {"type": "string"}, "accountStatus": {"type": "string"}, "name": {"type": "string"}, "repCode": {"type": "string"}, "registrationType": {"type": "object", "properties": {"name": {"type": "string"}, "code": {"type": "string"}}}, "dailyBalances": {"type": "object", "properties": {"endingBalance": {"type": "number", "format": "currency"}, "endingCashBalance": {"type": "number", "format": "currency"}, "endingMarginBalance": {"type": "number", "format": "currency"}, "endingMarketValue": {"type": "number", "format": "currency"}, "endingMoneyMarketBalance": {"type": "number", "format": "currency"}, "maintenanceCall": {"type": "number", "format": "currency"}}}}}, "AdvisorPosition": {"type": "object", "properties": {"positionId": {"type": "string"}, "accountNumber": {"type": "string"}, "accountName": {"type": "string"}, "securitySymbol": {"type": "string"}, "securityName": {"type": "string"}, "quantity": {"type": "number"}, "marketValue": {"type": "number", "format": "currency"}, "price": {"type": "number", "format": "currency"}}}, "Security": {"type": "object", "properties": {"id": {"type": "string"}, "symbol": {"type": "string"}, "name": {"type": "string"}, "cusip": {"type": "string"}, "securityType": {"type": "string"}, "exchange": {"type": "string"}}}, "Position": {"type": "object", "properties": {"positionId": {"type": "string"}, "accountNumber": {"type": "string"}, "accountName": {"type": "string"}, "accountId": {"type": "string"}, "securityId": {"type": "string"}, "securitySymbol": {"type": "string"}, "securityName": {"type": "string"}, "quantity": {"type": "number"}, "marketValue": {"type": "number", "format": "currency"}, "price": {"type": "number", "format": "currency"}, "costBasis": {"type": "number", "format": "currency"}, "unrealizedGainLoss": {"type": "number", "format": "currency"}}}, "Transaction": {"type": "object", "properties": {"transactionId": {"type": "string"}, "accountNumber": {"type": "string"}, "accountName": {"type": "string"}, "transactionDate": {"type": "string", "format": "date"}, "settleDate": {"type": "string", "format": "date"}, "transactionType": {"type": "string"}, "description": {"type": "string"}, "amount": {"type": "number", "format": "currency"}, "quantity": {"type": "number"}, "price": {"type": "number", "format": "currency"}, "symbol": {"type": "string"}}}, "Client": {"type": "object", "properties": {"clientId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "fullName": {"type": "string"}, "endingBalance": {"type": "number", "format": "currency"}, "numberOfAccounts": {"type": "integer"}, "primaryAddress": {"type": "object", "properties": {"street": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zipCode": {"type": "string"}}}}}, "AccountSearchResult": {"type": "object", "properties": {"accountNumber": {"type": "string"}, "accountName": {"type": "string"}, "clientName": {"type": "string"}, "repCode": {"type": "string"}, "accountStatus": {"type": "string"}, "registrationType": {"type": "string"}}}, "AccountProfile": {"type": "object", "properties": {"accountId": {"type": "string"}, "accountNumber": {"type": "string"}, "accountName": {"type": "string"}, "accountStatus": {"type": "string"}, "openDate": {"type": "string", "format": "date"}, "closeDate": {"type": "string", "format": "date"}, "registrationType": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}}}, "investmentObjective": {"type": "string"}, "riskTolerance": {"type": "string"}, "repCode": {"type": "string"}, "owners": {"type": "array", "items": {"type": "object", "properties": {"ownerId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "ownerType": {"type": "string"}}}}, "currentBalances": {"type": "object", "properties": {"totalValue": {"type": "number", "format": "currency"}, "cashBalance": {"type": "number", "format": "currency"}, "marginBalance": {"type": "number", "format": "currency"}, "marketValue": {"type": "number", "format": "currency"}}}}}}}, "tags": [{"name": "Accounts", "description": "Account related operations"}, {"name": "Balances", "description": "Balance related operations"}, {"name": "Portfolio", "description": "Portfolio allocation operations"}, {"name": "Advisor", "description": "Advisor specific operations"}, {"name": "Service Requests", "description": "Service request operations"}, {"name": "Positions", "description": "Position and holdings operations"}, {"name": "Securities", "description": "Security search and information"}, {"name": "Activities", "description": "Transaction and activity operations"}, {"name": "Clients", "description": "Client search and management"}]}