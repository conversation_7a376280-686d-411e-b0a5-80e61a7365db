import json
import os
import sys
import asyncio
from venv import logger
import requests
import logging

from pathlib import Path
from typing import Any, Dict, List, Optional


from google.adk.tools.openapi_tool.openapi_spec_parser import (
    OpenAPIToolset,
    OpenApiSpecParser,
    RestApiTool,
    ToolAuthHandler,
)
from google.adk.tools.openapi_tool.auth.auth_helpers import token_to_scheme_credential
from google.adk.tools import ToolContext

sys.path.append(str(Path(__file__).parent.parent.parent))

from core.auth import get_headers
from core.config import AUTH_PROPERTIES, SERVICES_PROPERTIES

BASE_PATH = os.path.dirname(os.path.abspath(__file__))

logger = logging.getLogger(__name__)

class CustomRestApiTool(RestApiTool):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    async def call(
        self, *, args: dict[str, Any], tool_context: Optional[ToolContext]
    ) -> Dict[str, Any]:
        """Executes the REST API call.

        Args:
            args: Keyword arguments representing the operation parameters.
            tool_context: The tool context (not used here, but required by the
            interface).

        Returns:
            The API response as a dictionary.
        """
        # Prepare auth credentials for the API call
        tool_auth_handler = ToolAuthHandler.from_tool_context(
            tool_context, self.auth_scheme, self.auth_credential
        )
        auth_result = await tool_auth_handler.prepare_auth_credentials()
        auth_state, auth_scheme, auth_credential = (
            auth_result.state,
            auth_result.auth_scheme,
            auth_result.auth_credential,
        )

        if auth_state == "pending":
            return {
                "pending": True,
                "message": "Needs your authorization to access your data.",
            }

        # Attach parameters from auth into main parameters list
        api_params, api_args = self._operation_parser.get_parameters().copy(), args
        if auth_credential:
            # Attach parameters from auth into main parameters list
            auth_param, auth_args = self._prepare_auth_request_params(
                auth_scheme, auth_credential
            )
            if auth_param and auth_args:
                api_params = [auth_param] + api_params
                api_args.update(auth_args)

        # Got all parameters. Call the API.
        request_params = self._prepare_request_params(api_params, api_args)

        auth_headers = await get_headers()
        current_headers = request_params.get("headers", {})
        current_headers.update(auth_headers)
        request_params["headers"] = current_headers
        
        logger.info(f"Calling API {request_params.get("url")}")
        response = requests.request(**request_params)

        # Parse API response
        try:
            response.raise_for_status()  # Raise HTTPError for bad responses
            return response.json()  # Try to decode JSON
        except requests.exceptions.HTTPError:
            error_details = response.content.decode("utf-8")
            return {
                "error": (
                    f"Tool {self.name} execution failed. Analyze this execution error"
                    " and your inputs. Retry with adjustments if applicable. But"
                    " make sure don't retry more than 3 times. Execution Error:"
                    f" {error_details}"
                )
            }
        except ValueError:
            return {"text": response.text}  # Return text if not JSON


class CustomOpenAPIToolset(OpenAPIToolset):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _parse(self, openapi_spec_dict: Dict[str, Any]) -> List[RestApiTool]:
        """Parse OpenAPI spec into a list of RestApiTool."""
        operations = OpenApiSpecParser().parse(openapi_spec_dict)

        tools = []
        for o in operations:
            tool = CustomRestApiTool.from_parsed_operation(o)
            tools.append(tool)
        return tools


openapi_spec_path = os.path.join(BASE_PATH, "./data/openapi_spec.json")
with open(openapi_spec_path, "r") as fp:
    openapi_spec_dict = json.load(fp)

openapi_spec_dict["servers"] = [
    {"url": SERVICES_PROPERTIES.BASE_URL, "description": "Base URL"}
]

toolset = CustomOpenAPIToolset(spec_dict=openapi_spec_dict)
