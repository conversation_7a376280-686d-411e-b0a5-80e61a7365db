import os
import sys
import asyncio

from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent))

from core.config import AUTH_PROPERTIES

os.environ["GOOGLE_CLOUD_PROJECT"] = AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT
os.environ["GOOGLE_CLOUD_LOCATION"] = AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION
os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "TRUE"


from google.adk.agents import Agent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types
from google.adk.tools.agent_tool import AgentTool

from agents.advisor_agent.dynamic_query import call_dynamic_query, DynamicQueryGenerator
from agents.advisor_agent.api_tools import toolset

BASE_PATH = os.path.dirname(os.path.abspath(__file__))


async def dynamic_query_tool(query: str):
    """
    Executes a natural language query and returns the response.

    This function takes a user query for retrieving data from the database in plain language and returns the data in the form of a JSON object.

    Args:
        query (str): The query to be executed.
    Returns:
        dict: The response from the query execution.
    """
    try:
        generator = DynamicQueryGenerator()
        payload = await generator.generate(query)
        if payload and isinstance(payload, dict):
            result = await call_dynamic_query(payload)
            return result
        else:
            return payload
    except Exception as e:
        print("Error while executing query:", e)

    return "Unable to execute the query"


with open(os.path.join(BASE_PATH, "./prompts/advisor_prompt.md"), "r") as f:
    advisor_instruction = f.read()

with open(os.path.join(BASE_PATH, "./prompts/api_agent.md"), "r") as f:
    api_agent_instruction = f.read()

api_agent = Agent(
    name="API_Agent",
    model="gemini-2.0-flash",
    description="Agent to retrieve data related to the user queries from the provided APIs",
    instruction=api_agent_instruction,
    tools=[toolset],
)

root_agent = Agent(
    name="financial_advisor",
    model="gemini-2.0-flash",
    description=("wealth advisor agent to answer questions about wealth management"),
    instruction=advisor_instruction,
    tools=[AgentTool(agent=api_agent)],
)


async def execute_query(query: str):
    APP_NAME = "advisor"
    USER_ID = "user_1"

    session_service = InMemorySessionService()

    """Sends a query to the agent and prints the final response."""

    print(f"\n>>> User Query: {query}")

    # Create a session
    session = await session_service.create_session(
        app_name=APP_NAME,
        user_id=USER_ID,
    )

    # Create a Runner
    runner = Runner(
        app_name=APP_NAME,
        agent=root_agent,
        session_service=session_service,
    )

    # Prepare the user's message in ADK format
    content = types.Content(role="user", parts=[types.Part(text=query)])

    final_response_text = None
    # We iterate through events from run_async to find the final answer.
    async for event in runner.run_async(
        user_id=USER_ID, session_id=session.id, new_message=content
    ):
        if event.is_final_response():
            if event.content and event.content.parts:
                final_response_text = event.content.parts[0].text
            break
    print(f"<<< Agent Response: {final_response_text}")


if __name__ == "__main__":
    query = "Can you show all accounts in california"
    asyncio.run(execute_query(query))
